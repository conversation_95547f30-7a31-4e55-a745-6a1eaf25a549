{"version": "6", "dialect": "sqlite", "id": "53e0cd4f-4ab4-4d82-8ed5-4dc5b0af0929", "prevId": "d26929b7-d287-4bc8-80e4-fe5f005af414", "tables": {"conversations": {"name": "conversations", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {}, "foreignKeys": {"conversations_user_id_users_id_fk": {"name": "conversations_user_id_users_id_fk", "tableFrom": "conversations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "messages": {"name": "messages", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true, "autoincrement": false}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "is_loading": {"name": "is_loading", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "doc_references": {"name": "doc_references", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "statistics": {"name": "statistics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "totalRequestTimes": {"name": "totalRequestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "totalTokenUsage": {"name": "totalTokenUsage", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "dingTalkUnionId": {"name": "dingTalkUnionId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "isAdmin": {"name": "isAdmin", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": false}, "token": {"name": "token", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 20000}, "requestTimes": {"name": "requestTimes", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "dingTalkUserId": {"name": "dingTalkUserId", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "mobile": {"name": "mobile", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}, "updatedAt": {"name": "updatedAt", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "(datetime('now'))"}}, "indexes": {"users_dingTalkUnionId_unique": {"name": "users_dingTalkUnionId_unique", "columns": ["dingTalkUnionId"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}