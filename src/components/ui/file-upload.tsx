import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useImageUpload } from "@/components/hooks/use-image-upload";
import { ImagePlus, X, Upload, Trash2 } from "lucide-react";
import { useCallback, useState } from "react";
import { cn } from "@/lib/utils";

/**
 * 文件上传组件属性接口
 */
interface FileUploadProps {
	onFileSelect?: (file: File | null) => void;
}

/**
 * 文件上传组件
 * 支持拖拽上传和点击上传功能
 */
export function FileUpload({ onFileSelect }: FileUploadProps) {
	const {
		previewUrl,
		fileName,
		fileInputRef,
		handleThumbnailClick,
		handleFileChange,
		handleRemove,
	} = useImageUpload({
		onUpload: (url) => console.log("Uploaded image URL:", url),
		onFileSelect: onFileSelect, // 传递文件选择回调
	});

	const [isDragging, setIsDragging] = useState(false);

	/**
	 * 处理拖拽悬停
	 */
	const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
	};

	/**
	 * 处理拖拽进入
	 */
	const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(true);
	};

	/**
	 * 处理拖拽离开
	 */
	const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
		e.preventDefault();
		e.stopPropagation();
		setIsDragging(false);
	};

	/**
	 * 处理文件拖拽放置
	 */
	const handleDrop = useCallback(
		(e: React.DragEvent<HTMLDivElement>) => {
			e.preventDefault();
			e.stopPropagation();
			setIsDragging(false);

			const file = e.dataTransfer.files?.[0];
			if (file) {
				// 直接处理文件，避免创建假的事件对象
				if (fileInputRef.current) {
					// 创建一个新的FileList对象
					const dataTransfer = new DataTransfer();
					dataTransfer.items.add(file);
					fileInputRef.current.files = dataTransfer.files;
					
					// 触发change事件
					const event = new Event('change', { bubbles: true });
					fileInputRef.current.dispatchEvent(event);
				}
			}
		},
		[fileInputRef]
	);

	return (
		<div className="w-full max-w-md space-y-6 rounded-xl border border-border bg-card p-6 shadow-sm">
			<div className="space-y-2">
				<h3 className="text-lg font-medium">文件上传</h3>
				<p className="text-sm text-muted-foreground">
					支持格式: JPG, PNG, GIF, PDF, DOC, DOCX
				</p>
			</div>

			<Input
				type="file"
				accept="image/*,.pdf,.doc,.docx"
				className="hidden"
				ref={fileInputRef}
				onChange={handleFileChange}
			/>

			{!previewUrl ? (
				<div
					onClick={handleThumbnailClick}
					onDragOver={handleDragOver}
					onDragEnter={handleDragEnter}
					onDragLeave={handleDragLeave}
					onDrop={handleDrop}
					className={cn(
						"flex h-64 cursor-pointer flex-col items-center justify-center gap-4 rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/50 transition-colors hover:bg-muted",
						isDragging && "border-primary/50 bg-primary/5"
					)}
				>
					<div className="rounded-full bg-background p-3 shadow-sm">
						<ImagePlus className="h-6 w-6 text-muted-foreground" />
					</div>
					<div className="text-center">
						<p className="text-sm font-medium">点击选择文件</p>
						<p className="text-xs text-muted-foreground">
							或拖拽文件到此处
						</p>
					</div>
				</div>
			) : (
				<div className="relative">
					<div className="group relative h-64 overflow-hidden rounded-lg border">
						{/* 使用普通的img标签替代Next.js的Image组件 */}
						<img
							src={previewUrl}
							alt="Preview"
							className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
						/>
						<div className="absolute inset-0 bg-black/40 opacity-0 transition-opacity group-hover:opacity-100" />
						<div className="absolute inset-0 flex items-center justify-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
							<Button
								size="sm"
								variant="secondary"
								onClick={handleThumbnailClick}
								className="h-9 w-9 p-0"
							>
								<Upload className="h-4 w-4" />
							</Button>
							<Button
								size="sm"
								variant="destructive"
								onClick={handleRemove}
								className="h-9 w-9 p-0"
							>
								<Trash2 className="h-4 w-4" />
							</Button>
						</div>
					</div>
					{fileName && (
						<div className="mt-2 flex items-center gap-2 text-sm text-muted-foreground">
							<span className="truncate">{fileName}</span>
							<button
								onClick={handleRemove}
								className="ml-auto rounded-full p-1 hover:bg-muted"
							>
								<X className="h-4 w-4" />
							</button>
						</div>
					)}
				</div>
			)}
		</div>
	);
}