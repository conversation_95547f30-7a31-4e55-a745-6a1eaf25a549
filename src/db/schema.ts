import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';
import { sql, relations } from 'drizzle-orm';

// 用户表
export const users = sqliteTable('users', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	// 使用dingTalkUnionId作为主要标识
	dingTalkUnionId: text('dingTalkUnionId').notNull().unique(),
	isAdmin: integer('isAdmin', { mode: 'boolean' }).notNull().default(false),
	token: integer('token').notNull().default(20000),
	requestTimes: integer('requestTimes').notNull().default(0),
	dingTalkUserId: text('dingTalkUserId'),
	name: text('name'),
	avatar: text('avatar'),
	mobile: text('mobile'),
	createdAt: text('createdAt').notNull().default(sql`(datetime('now'))`),
	updatedAt: text('updatedAt').notNull().default(sql`(datetime('now'))`),
});




// 导出类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;

export const usersRelations = relations(users, ({ many }) => ({
	conversations: many(conversations),
}));

// 对话表
export const conversations = sqliteTable('conversations', {
	id: text('id').primaryKey(),
	userId: integer('user_id')
		.notNull()
		.references(() => users.id, { onDelete: 'cascade' }),
	title: text('title').notNull(),
	createdAt: text('createdAt')
		.notNull()
		.default(sql`(datetime('now'))`),
	updatedAt: text('updatedAt')
		.notNull()
		.default(sql`(datetime('now'))`),
});

export const conversationsRelations = relations(conversations, ({ one, many }) => ({
	user: one(users, {
		fields: [conversations.userId],
		references: [users.id],
	}),
	messages: many(messages),
}));

// 消息表
export const messages = sqliteTable('messages', {
	id: text('id').primaryKey(),
	conversationId: text('conversation_id')
		.notNull()
		.references(() => conversations.id, { onDelete: 'cascade' }),
	content: text('content').notNull(),
	role: text('role', { enum: ['user', 'assistant'] }).notNull(),
	timestamp: text('timestamp')
		.notNull()
		.default(sql`(datetime('now'))`),
});

export const messagesRelations = relations(messages, ({ one }) => ({
	conversation: one(conversations, {
		fields: [messages.conversationId],
		references: [conversations.id],
	}),
}));

export type Conversation = typeof conversations.$inferSelect;
export type NewConversation = typeof conversations.$inferInsert;
export type Message = typeof messages.$inferSelect;
export type NewMessage = typeof messages.$inferInsert;

// 统计信息表
export const statistics = sqliteTable('statistics', {
	id: integer('id').primaryKey({ autoIncrement: true }),
	totalRequestTimes: integer('totalRequestTimes').notNull().default(0),
	totalTokenUsage: integer('totalTokenUsage').notNull().default(0),
});

// 导出类型
export type Statistic = typeof statistics.$inferSelect;
export type NewStatistic = typeof statistics.$inferInsert;