'use server';

import { db } from '@/db';
import { conversations, messages } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function createConversation(userId: number, title: string) {
  const newConversation = await db
    .insert(conversations)
    .values({ userId, title, id: `conv_${Date.now()}` })
    .returning();
  return newConversation[0];
}

export async function getConversations(userId: number) {
  return db.query.conversations.findMany({
    where: eq(conversations.userId, userId),
    orderBy: (conversations, { desc }) => [desc(conversations.updatedAt)],
  });
}

export async function getConversation(conversationId: string) {
  return db.query.conversations.findFirst({
    where: eq(conversations.id, conversationId),
    with: {
      messages: {
        orderBy: (messages, { asc }) => [asc(messages.timestamp)],
      },
    },
  });
}

export async function addMessage({ conversationId, content, role, isLoading, docReferences, }: { conversationId: string; content: string; role: 'user' | 'assistant'; isLoading?: boolean; docReferences?: any; }) {
  const newMessage = await db
    .insert(messages)
    .values({
      id: `msg_${Date.now()}`,
      conversationId,
      content,
      role,
      isLoading,
      docReferences: docReferences ? JSON.stringify(docReferences) : undefined,
    })
    .returning();

  await db
    .update(conversations)
    .set({ updatedAt: new Date().toISOString() })
    .where(eq(conversations.id, conversationId));

  return newMessage[0];
}

export async function updateConversationTitle(conversationId: string, title: string) {
  const updatedConversation = await db
    .update(conversations)
    .set({ title, updatedAt: new Date().toISOString() })
    .where(eq(conversations.id, conversationId))
    .returning();
  return updatedConversation[0];
}

export async function deleteConversation(conversationId: string) {
  await db.delete(conversations).where(eq(conversations.id, conversationId));
}

export async function updateMessage(messageId: string, { content, isLoading, docReferences }: { content?: string; isLoading?: boolean; docReferences?: any; }) {
  const updatedMessage = await db
    .update(messages)
    .set({
      content,
      isLoading,
      docReferences: docReferences ? JSON.stringify(docReferences) : undefined,
    })
    .where(eq(messages.id, messageId))
    .returning();

  return updatedMessage[0];
}